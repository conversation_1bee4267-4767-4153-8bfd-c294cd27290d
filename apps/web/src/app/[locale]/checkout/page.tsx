'use client'

import { useEffect, useMemo, useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import {
  PRECISE_RATE_LIMIT,
  TCheckoutCustomerCart,
  TRACK_EVENT,
  useGetCustomerCartQuery,
  useLoadingContext,
  useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'
import { useNavigate, usePlaceOrder, useUserAddress } from '@ninebot/core/src/businessHooks'
import { DELIVERY_METHOD_TYPE } from '@ninebot/core/src/constants'
import {
  cartIdSelector,
  checkoutGroupedProductsSelector,
  checkoutHasInsuranceItemSelector,
  checkoutIsInitProcessCompletedSelector,
  checkoutIsNeedSetShippingAddressAndBillingAddressSelector,
  checkoutIsNeedSetShippingMethodsSelector,
  checkoutNCoinPayTotalSelector,
  checkoutPaymentMethodsSelector,
  checkoutPlaceOrderAddressIdSelector,
  checkoutPricesSelector,
  checkoutSelectedPaymentMethodSelector,
  checkoutSelectedShippingMethodSelector,
  checkoutShippingMethodsSelector,
  // resetCheckout,
  setCheckoutData,
  setCheckoutInitProcessComplete,
  setCheckoutPlaceOrderAddressId,
  setCheckoutPlaceOrderPaymentMethodCode,
  setCheckoutPlaceOrderShippingMethodCode,
  setCheckoutUserNCoinInfo,
  storeConfigSelector,
} from '@ninebot/core/src/store'
import { useAppDispatch, useAppSelector } from '@ninebot/core/src/store/hooks'
import { isUndefined } from 'lodash-es'

import { AddressSection, Skeleton } from '@/components'
import { CartItem } from '@/types/checkout'

import {
  BottomActionBar,
  InsuranceFormCard,
  InvoiceSection,
  PaymentSection,
  PriceSummary,
  ProductSection,
} from './_components'
// 定义验证结果类型
type ValidationResult = string | { [key: string]: string | number | boolean }

// 定义验证器接口
interface Validator {
  validate: () => Promise<ValidationResult | null>
  scrollTo: () => void
}

// 定义验证引用类型
interface ValidatorRef {
  [key: string]: Validator
}

/**
 * 结算页面
 */
const Page = () => {
  const [selectedInvoice, setSelectedInvoice] = useState('0')
  // const [selectedPayment, setSelectedPayment] = useState('微信支付')
  // const items = mockCartItems
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)

  // const pageParams = parseToKeyValueObject(props?.route?.params?.pageParams)
  const dispatch = useAppDispatch()
  const toast = useToastContext()
  const loading = useLoadingContext()
  const getI18nString = useTranslations('Common')
  const { reportEvent } = useVolcAnalytics()
  const { openPage } = useNavigate()
  // const { top: safeAreaTop, bottom: safeAreaBottom } = useSafeAreaInsets()

  const groupedProducts = useAppSelector(checkoutGroupedProductsSelector)
  const cartId = useAppSelector(cartIdSelector)
  const shippingMethods = useAppSelector(checkoutShippingMethodsSelector)
  const paymentMethods = useAppSelector(checkoutPaymentMethodsSelector)
  const placeOrderAddressId = useAppSelector(checkoutPlaceOrderAddressIdSelector)
  const isNeedSetShippingAddressAndBillingAddress = useAppSelector(
    checkoutIsNeedSetShippingAddressAndBillingAddressSelector,
  )
  const isNeedSetShippingMethods = useAppSelector(checkoutIsNeedSetShippingMethodsSelector)
  const selectedShippingMethod = useAppSelector(checkoutSelectedShippingMethodSelector)
  const selectedPaymentMethod = useAppSelector(checkoutSelectedPaymentMethodSelector)
  const hasInsuranceItem = useAppSelector(checkoutHasInsuranceItemSelector)
  const isInitProcessCompleted = useAppSelector(checkoutIsInitProcessCompletedSelector)
  const prices = useAppSelector(checkoutPricesSelector)
  const NCoinPayTotal = useAppSelector(checkoutNCoinPayTotalSelector)
  const storeConfig = useAppSelector(storeConfigSelector)
  const scrollViewRef = useRef<HTMLDivElement>(null)
  const validatorRef = useRef<ValidatorRef>({})
  const [showContent, setShowContent] = useState(false)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  // 控制只上报一次曝光事件
  const reportFlag = useRef(false)

  const { fetchUserAddresses, userAddressDefault, firstUserAddress } = useUserAddress()
  const {
    handleSetShippingAddressesOnCart,
    handleSetBillingAddressOnCart,
    handleSetShippingMethodsOnCart,
  } = usePlaceOrder()
  const {
    currentData: customerCartData,
    isError,
    error,
  } = useGetCustomerCartQuery({
    currentPage: 1,
    pageSize: 1000,
  })

  /**
   * 是否展示支付方式
   */
  const isShowPayment = useMemo(() => {
    return selectedPaymentMethod && selectedPaymentMethod?.code !== 'free'
  }, [selectedPaymentMethod])

  /**
   * 骨架屏处理
   */
  useEffect(() => {
    if (isInitProcessCompleted) {
      timeoutRef.current = setTimeout(() => {
        setShowContent(true)
      }, 15)
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }
    }
  }, [isInitProcessCompleted])

  /**
   * 错误提示
   */
  useEffect(() => {
    if (isError && error) {
      // RTK Query 错误结构检查
      const errorType = 'type' in error ? error.type : undefined
      console.log('Checkout page error:', {
        error,
        errorType,
        isRateLimit: errorType === PRECISE_RATE_LIMIT,
      })

      // 检查是否为 427/428 限流错误
      if (errorType === PRECISE_RATE_LIMIT) {
        console.log('Rate limit detected, redirecting to home in 3 seconds')
        // 显示 toast 提示
        const errorMessage = 'data' in error ? String(error.data) : '当前访问人数过多，请稍后再试！'
        toast.show({
          icon: 'fail',
          content: errorMessage,
        })

        // 3 秒后重定向到首页
        setTimeout(() => {
          console.log('Redirecting to home page due to rate limit')
          openPage({
            route: 'home',
            replace: true,
          })
        }, 3000)
      } else {
        // 其他错误正常显示 toast
        const errorMessage =
          'data' in error ? String(error.data) : getI18nString('fetch_data_error')
        toast.show({
          icon: 'fail',
          content: errorMessage,
        })
      }
    }
  }, [toast, isError, error, getI18nString, openPage])

  /**
   * 保存 checkout 数据
   */
  useEffect(() => {
    if (customerCartData?.customerCart) {
      dispatch(setCheckoutData(customerCartData.customerCart))
    }
    if (customerCartData?.check_nb_info) {
      dispatch(setCheckoutUserNCoinInfo(customerCartData.check_nb_info))
    }
  }, [customerCartData, dispatch])

  /**
   * 每次进入结算页，都获取最新地址信息
   */
  useEffect(() => {
    fetchUserAddresses()
  }, [fetchUserAddresses])

  /**
   * 如果没有下单地址 ID，优先使用默认地址 ID 或地址簿中的第 1个地址 ID
   */
  useEffect(() => {
    if (!placeOrderAddressId && userAddressDefault?.address_id) {
      dispatch(setCheckoutPlaceOrderAddressId(userAddressDefault.address_id))
    } else if (!placeOrderAddressId && firstUserAddress?.address_id) {
      dispatch(setCheckoutPlaceOrderAddressId(firstUserAddress.address_id))
    }
  }, [dispatch, userAddressDefault, placeOrderAddressId, firstUserAddress])

  /**
   * 结算流程 1，默认选中 shipping method & payment method
   */
  useEffect(() => {
    if (isNeedSetShippingAddressAndBillingAddress) {
      // 设置默认的 shipping method
      if (shippingMethods?.length > 0 && !selectedShippingMethod) {
        // 获取第一个 shipping method
        const [firstShippingMethod] = shippingMethods
        dispatch(setCheckoutPlaceOrderShippingMethodCode(firstShippingMethod?.method_code))
      }
    }

    // 设置默认的 payment method
    if (paymentMethods?.length > 0 && !selectedPaymentMethod) {
      // 获取第一个 payment method
      const [firstPaymentMethod] = paymentMethods
      dispatch(setCheckoutPlaceOrderPaymentMethodCode(firstPaymentMethod?.code))
    }
  }, [
    dispatch,
    paymentMethods,
    shippingMethods,
    isNeedSetShippingAddressAndBillingAddress,
    selectedShippingMethod,
    selectedPaymentMethod,
  ])

  /**
   * 结算流程 2
   */
  useEffect(() => {
    const initCheckoutProcess = async () => {
      if (!cartId) {
        return
      }

      // 需要设置 shipping address and billing address
      if (
        isNeedSetShippingAddressAndBillingAddress &&
        placeOrderAddressId &&
        isNeedSetShippingMethods &&
        selectedShippingMethod
      ) {
        loading.show()
        // 同时设置 shipping address and billing address
        const setAddressResult = await Promise.all([
          handleSetShippingAddressesOnCart({
            cart_id: cartId,
            shipping_addresses: [{ customer_address_id: Number(placeOrderAddressId) }],
          }),
          handleSetBillingAddressOnCart({
            cart_id: cartId,
            billing_address: {
              customer_address_id: Number(placeOrderAddressId),
            },
          }),
        ])

        if (setAddressResult.every(Boolean)) {
          // 设置 shipping methods
          const setShippingMethodsResult = await handleSetShippingMethodsOnCart({
            cart_id: cartId,
            shipping_methods: [
              {
                carrier_code: selectedShippingMethod.carrier_code || '',
                method_code: selectedShippingMethod.method_code || '',
              },
            ],
          })

          // 根据返回结果，更新 prices
          if (setShippingMethodsResult?.cart?.id) {
            // 类型转换，确保符合Cart类型
            const cartData = setShippingMethodsResult.cart as TCheckoutCustomerCart
            dispatch(setCheckoutData(cartData))
            // 完成结算流程
            dispatch(setCheckoutInitProcessComplete(true))
          }
        }
        loading.hide()
        return
      }

      // 不需要设置 shipping address and billing address
      if (!isNeedSetShippingAddressAndBillingAddress || !firstUserAddress) {
        // 完成结算流程
        dispatch(setCheckoutInitProcessComplete(true))
      }
    }

    initCheckoutProcess()
  }, [
    dispatch,
    placeOrderAddressId,
    cartId,
    handleSetShippingAddressesOnCart,
    handleSetBillingAddressOnCart,
    isNeedSetShippingAddressAndBillingAddress,
    handleSetShippingMethodsOnCart,
    isNeedSetShippingMethods,
    selectedShippingMethod,
    loading,
    firstUserAddress,
  ])

  /**
   * 点击返回按钮
   */
  // const handleNavBarLeftPress = useCallback(() => {
  //   dispatch(resetCheckout())

  //   return true
  // }, [dispatch])

  // 埋点：商品结算
  useEffect(() => {
    if (
      !isUndefined(prices?.grand_total?.value) &&
      !isUndefined(NCoinPayTotal?.grand_total) &&
      !reportFlag.current
    ) {
      reportFlag.current = true
      reportEvent(TRACK_EVENT.shop_commodity_settlement_exposure, {
        cash_amount: prices.grand_total.value,
        N_amount: NCoinPayTotal.grand_total,
      })
    }
  }, [prices, NCoinPayTotal, reportEvent])

  return (
    <div className="max-container-no-mb mb-[58px] mt-[60px]">
      {/* 标题 */}
      <div className="mb-[48px] flex items-center">
        <h2 className="font-miSansDemiBold450 text-h2">购物结算</h2>
      </div>

      <div className="flex">
        {!showContent ? (
          <>
            <div className="flex-1">
              <Skeleton style={{ height: 800, width: '100%' }} />
            </div>

            <div className="mx-[40px] w-[1px] border-r border-[#00000015] 3xl:mx-[88px]" />

            <div className="w-full max-w-[368px] 2xl:max-w-[380px] 3xl:max-w-[412px]">
              <Skeleton style={{ height: 400, width: '100%' }} />
            </div>
          </>
        ) : (
          <>
            {/* 左侧主要内容 */}
            <div className="flex-1">
              <div className="flex flex-1 flex-col gap-base-64">
                {/* 收货地址 */}
                {isNeedSetShippingAddressAndBillingAddress && (
                  <AddressSection
                    isAddModalOpen={isAddModalOpen}
                    onAddModalClose={() => setIsAddModalOpen(false)}
                    onAddModalOpen={() => setIsAddModalOpen(true)}
                  />
                )}

                {/* 保单表单组件 */}
                {hasInsuranceItem ? (
                  <InsuranceFormCard
                    ref={(ref) => {
                      if (validatorRef.current && ref) {
                        validatorRef.current.insurance_information = ref
                      }
                    }}
                    scrollViewRef={scrollViewRef}
                  />
                ) : null}

                {/* 自提 / 普通商品列表 */}
                {groupedProducts.map((group) => {
                  if (DELIVERY_METHOD_TYPE.store_pickup === group.code) {
                    return (
                      <ProductSection
                        key={group.code}
                        showType="pickup"
                        ref={(ref) => {
                          if (validatorRef.current && ref) {
                            validatorRef.current[group.code] = ref
                          }
                        }}
                        scrollViewRef={scrollViewRef}
                        title={group.title}
                        products={group.products as unknown as CartItem[]}
                      />
                    )
                  }

                  return (
                    <ProductSection
                      key={group.code}
                      title={group.title}
                      products={group.products as unknown as CartItem[]}
                    />
                  )
                })}

                {/* 发票信息 */}
                {storeConfig?.enable_checkout_invoice && (
                  <InvoiceSection
                    ref={(ref) => {
                      if (validatorRef.current && ref && selectedInvoice !== '0') {
                        validatorRef.current.invoice = ref
                      } else {
                        delete validatorRef.current.invoice
                      }
                    }}
                    selectedInvoice={selectedInvoice}
                    setSelectedInvoice={setSelectedInvoice}
                  />
                )}

                {/* 支付方式 */}
                {isShowPayment ? <PaymentSection /> : null}
              </div>
            </div>

            <div className="mx-[40px] w-[1px] border-r border-[#00000015] 3xl:mx-[88px]" />

            {/* 右侧价格明细 */}
            <div className="w-full max-w-[368px] 2xl:max-w-[380px] 3xl:max-w-[412px]">
              <div className="sticky top-[76px] z-10 bg-white">
                <PriceSummary />
                <BottomActionBar validatorRef={validatorRef} />
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default Page
