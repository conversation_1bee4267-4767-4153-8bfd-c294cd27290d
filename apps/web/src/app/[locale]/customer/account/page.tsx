'use client'
import { useCallback, useEffect, useState } from 'react'
import {
  Customer,
  GqlError,
  PRECISE_RATE_LIMIT,
  resolveCatchMessage,
  TRACK_EVENT,
  useLazyGetAccountCenterInfoQuery,
  useToastContext,
  useVolcAnalytics,
} from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'

import { OrderList } from '@/businessComponents'
import { Skeleton, UserProfile } from '@/components'

export default function AccountPage() {
  const toast = useToastContext()
  const { reportEvent } = useVolcAnalytics()
  const { openPage } = useNavigate()

  const [loading, setLoading] = useState({
    customerLoading: true,
    orderStatusLoading: true,
    ordersLoading: true,
  })
  const [getAccountCenterInfo] = useLazyGetAccountCenterInfoQuery()
  const [customer, setCustomer] = useState<Customer>({
    info: {
      email: '',
      name: '',
      phone: '',
      nCoin: 0,
      avatar: '',
    },
    orders: {
      pending: '0',
      processing: '0',
      shipped: '0',
      complete: '0',
    },
    services: {
      coupons: [],
    },
  })

  const getAccountInfo = useCallback(() => {
    getAccountCenterInfo({})
      .unwrap()
      .then((res) => {
        const accountInfo = res
        if (accountInfo?.customer) {
          const currentCustomer = accountInfo.customer
          const currentCouponsData = accountInfo.customer_coupons.items
          setCustomer((prevState) => ({
            ...prevState,
            info: {
              email: currentCustomer?.customer_email,
              name: currentCustomer?.customer_info?.customer_nickname,
              phone: currentCustomer?.customer_phone,
              nCoin: currentCustomer?.customer_ncoin,
              avatar: currentCustomer?.customer_info?.customer_logo,
            },
            orders: accountInfo.order,
            services: {
              coupons: currentCouponsData,
            },
          }))
          setLoading((pre) => ({
            ...pre,
            customerLoading: false,
          }))
        }
      })
      .catch((error) => {
        const err = error as GqlError

        // 检查是否为 427/428 限流错误
        if (err?.type === PRECISE_RATE_LIMIT) {
          // 显示 toast 提示
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error) as string,
          })

          // 3 秒后重定向到首页
          setTimeout(() => {
            openPage({
              route: 'home',
              replace: true,
            })
          }, 3000)
        } else {
          // 其他错误正常显示 toast
          toast.show({
            icon: 'fail',
            content: resolveCatchMessage(error) as string,
          })
        }
      })
  }, [getAccountCenterInfo, toast, openPage])

  useEffect(() => {
    getAccountInfo()
  }, [getAccountInfo])

  /**
   * 埋点：点击个人中心
   */
  useEffect(() => {
    reportEvent(TRACK_EVENT.shop_profile_page_exposure)
  }, [reportEvent])

  return (
    <div className="flex-1">
      {loading.customerLoading ? (
        <div className="mb-base-16 flex h-[327px] flex-col rounded-[20px] bg-white p-base-32">
          <div className="mb-[48px] flex h-[117px] w-full flex-row items-center justify-between">
            <div className="flex w-[64%] gap-base-24">
              <Skeleton shape="circle" style={{ width: 108, height: 108 }} />
              <Skeleton style={{ width: '40%', height: '108px' }} />
            </div>
            <Skeleton style={{ width: '33%' }} />
          </div>
          <div className="flex h-[170px] flex-row items-center justify-between">
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton key={index} style={{ width: '20%' }} />
            ))}
          </div>
        </div>
      ) : (
        <UserProfile customer={customer} />
      )}

      <OrderList isPage={false} externalStatus="" />
    </div>
  )
}
